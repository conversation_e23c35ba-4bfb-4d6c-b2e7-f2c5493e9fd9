"""
Ad schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any


class AdResponse(BaseModel):
    """Schema for ad response."""
    id: int
    adset_id: int
    ad_id: str
    name: str
    status: str
    creative_data: Optional[Dict[str, Any]] = None
    ad_creative_id: Optional[str] = None
    headline: Optional[str] = None
    body: Optional[str] = None
    call_to_action_type: Optional[str] = None
    link_url: Optional[str] = None
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    created_time: Optional[datetime] = None
    last_synced_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
