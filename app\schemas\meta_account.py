"""
Meta Account schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class MetaAccountBase(BaseModel):
    """Base Meta account schema."""
    account_id: str
    account_name: Optional[str] = None


class MetaAccountCreate(MetaAccountBase):
    """Schema for creating a Meta account."""
    access_token: str
    refresh_token: Optional[str] = None


class MetaAccountResponse(MetaAccountBase):
    """Schema for Meta account response."""
    id: int
    user_id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    token_expires_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
