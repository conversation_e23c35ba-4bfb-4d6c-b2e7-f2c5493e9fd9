# AD Engine Chatbot

AI-powered Meta Ads optimization chatbot that transforms raw advertising data into actionable, predictive insights.

## Project Overview

AD Engine is an intelligent chatbot that helps businesses optimize their Meta (Facebook/Instagram) advertising campaigns through AI-powered insights and recommendations. The system moves beyond basic reporting to provide intelligent optimization recommendations, saving time, reducing wasted spend, and identifying hidden opportunities.

## Technology Stack

- **Backend**: Python 3.11+ with FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **AI Integration**: OpenAI GPT-4 via Assistant API
- **Authentication**: OAuth 2.0 with Meta, JWT tokens
- **API Client**: httpx for async Meta API calls
- **Security**: Cryptography for token encryption
- **Migrations**: Alembic for database schema management

## Week 1 Implementation Status

### ✅ Completed Features

1. **Project Structure Setup**
   - FastAPI application with proper directory organization
   - Requirements.txt with all necessary dependencies
   - Environment configuration with .env support

2. **Database Models & Schema**
   - SQLAlchemy models for all core entities:
     - Users (authentication and user management)
     - MetaAccounts (encrypted Meta API credentials)
     - Campaigns, AdSets, Ads (Meta advertising hierarchy)
     - PerformanceMetrics (advertising performance data)
     - ChatSessions, ChatMessages (conversation management)

3. **Authentication System**
   - JWT token-based authentication
   - Password hashing with bcrypt
   - Meta OAuth 2.0 integration setup
   - Secure token encryption for Meta API credentials

4. **API Endpoints**
   - Authentication routes (`/auth/`)
   - Account management routes (`/api/accounts/`)
   - Chat functionality routes (`/api/chat/`)
   - Proper request/response validation with Pydantic

5. **Security & Utilities**
   - Token encryption/decryption utilities
   - Meta API client with async support
   - Authentication dependencies and middleware
   - Comprehensive error handling

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL database
- Meta Developer App (for OAuth)
- OpenAI API key

### Installation

1. **Clone and setup environment**:
   ```bash
   git clone <repository-url>
   cd Chatbot
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration values
   ```

3. **Setup database**:
   ```bash
   # Create PostgreSQL database
   createdb ad_engine_db
   
   # Initialize Alembic and create tables
   alembic init alembic
   alembic revision --autogenerate -m "Initial migration"
   alembic upgrade head
   ```

4. **Run the application**:
   ```bash
   uvicorn app.main:app --reload
   ```

5. **Access the API**:
   - API Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc
   - Health check: http://localhost:8000/health

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/meta/oauth` - Initiate Meta OAuth
- `POST /auth/meta/callback` - Handle OAuth callback

### Account Management
- `GET /api/accounts/` - List user's Meta accounts
- `GET /api/accounts/{id}/campaigns` - Get account campaigns
- `GET /api/accounts/{id}/performance` - Get performance data

### Chat Interface
- `GET /api/chat/sessions` - List chat sessions
- `POST /api/chat/sessions` - Create new session
- `GET /api/chat/sessions/{id}` - Get session details
- `POST /api/chat/message` - Send message to AI
- `GET /api/chat/sessions/{id}/history` - Get chat history

## Environment Variables

Required environment variables (see `.env.example`):

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/ad_engine_db

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key

# Meta API
META_APP_ID=your-meta-app-id
META_APP_SECRET=your-meta-app-secret

# Security
JWT_SECRET_KEY=your-jwt-secret-key
ENCRYPTION_KEY=your-32-byte-encryption-key
```

## Development Roadmap

### Week 2-3: Core Integration & AI Foundation
- OpenAI Assistant API integration
- Real-time Meta API data synchronization
- WebSocket support for live chat
- Enhanced error handling and logging

### Week 4-6: AI Intelligence & Analytics
- Predictive analytics engine
- Creative performance analysis
- Audience insights and persona generation
- Background task processing with Celery

### Week 7-8: Advanced Recommendations & Polish
- AI-generated optimization recommendations
- Budget and bidding strategy suggestions
- Comprehensive testing and deployment setup
- Production-ready optimizations

## Contributing

This is a development project following the specifications in `ad_engine_task.md`. 

## License

Private development project.
