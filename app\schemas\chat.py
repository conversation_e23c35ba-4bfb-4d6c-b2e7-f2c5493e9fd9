"""
Chat schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List


class ChatMessageCreate(BaseModel):
    """Schema for creating a chat message."""
    content: str
    session_id: Optional[int] = None


class ChatMessageResponse(BaseModel):
    """Schema for chat message response."""
    id: int
    session_id: int
    role: str
    content: str
    message_id: Optional[str] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class ChatSessionResponse(BaseModel):
    """Schema for chat session response."""
    id: int
    user_id: int
    thread_id: Optional[str] = None
    title: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_message_at: Optional[datetime] = None
    messages: Optional[List[ChatMessageResponse]] = None
    
    class Config:
        from_attributes = True
