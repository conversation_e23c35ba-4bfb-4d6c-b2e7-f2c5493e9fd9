"""
Performance Metric schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime, date
from typing import Optional


class PerformanceMetricResponse(BaseModel):
    """Schema for performance metric response."""
    id: int
    entity_id: str
    entity_type: str
    date: date
    impressions: int
    clicks: int
    spend: float
    conversions: int
    conversion_value: float
    reach: int
    frequency: float
    ctr: float
    cpc: float
    cpm: float
    roas: float
    cost_per_conversion: float
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
