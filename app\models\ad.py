"""
Ad model for storing Meta ad information.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class Ad(Base):
    """Ad model for storing Meta ad data."""
    
    __tablename__ = "ads"
    
    id = Column(Integer, primary_key=True, index=True)
    adset_id = Column(Integer, ForeignKey("adsets.id"), nullable=False)
    ad_id = Column(String, nullable=False, index=True)  # Meta ad ID
    name = Column(String, nullable=False)
    status = Column(String, nullable=False)  # ACTIVE, PAUSED, DELETED, etc.
    creative_data = Column(JSON, nullable=True)  # Creative elements (images, copy, etc.)
    ad_creative_id = Column(String, nullable=True)
    headline = Column(String, nullable=True)
    body = Column(String, nullable=True)
    call_to_action_type = Column(String, nullable=True)
    link_url = Column(String, nullable=True)
    image_url = Column(String, nullable=True)
    video_url = Column(String, nullable=True)
    created_time = Column(DateTime(timezone=True), nullable=True)
    additional_data = Column(JSON, nullable=True)  # Store additional Meta API data
    last_synced_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    adset = relationship("AdSet", back_populates="ads")
    performance_metrics = relationship("PerformanceMetric", back_populates="ad")
    
    def __repr__(self):
        return f"<Ad(id={self.id}, ad_id='{self.ad_id}', name='{self.name}')>"
