"""
AdSet model for storing Meta ad set information.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class AdSet(Base):
    """AdSet model for storing Meta ad set data."""
    
    __tablename__ = "adsets"
    
    id = Column(Integer, primary_key=True, index=True)
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=False)
    adset_id = Column(String, nullable=False, index=True)  # Meta adset ID
    name = Column(String, nullable=False)
    status = Column(String, nullable=False)  # ACTIVE, PAUSED, DELETED, etc.
    targeting = Column(JSON, nullable=True)  # Targeting criteria
    budget_remaining = Column(String, nullable=True)
    daily_budget = Column(String, nullable=True)
    lifetime_budget = Column(String, nullable=True)
    bid_amount = Column(String, nullable=True)
    optimization_goal = Column(String, nullable=True)
    billing_event = Column(String, nullable=True)
    created_time = Column(DateTime(timezone=True), nullable=True)
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    additional_data = Column(JSON, nullable=True)  # Store additional Meta API data
    last_synced_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    campaign = relationship("Campaign", back_populates="adsets")
    ads = relationship("Ad", back_populates="adset")
    performance_metrics = relationship("PerformanceMetric", back_populates="adset")
    
    def __repr__(self):
        return f"<AdSet(id={self.id}, adset_id='{self.adset_id}', name='{self.name}')>"
