"""
Chat Session model for storing chat session information.
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, DateTime, Foreign<PERSON><PERSON>, <PERSON>olean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class ChatSession(Base):
    """Chat Session model for storing chat session data."""
    
    __tablename__ = "chat_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    thread_id = Column(String, nullable=True, index=True)  # OpenAI thread ID
    title = Column(String, nullable=True)  # Session title/summary
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_message_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ChatSession(id={self.id}, user_id={self.user_id}, thread_id='{self.thread_id}')>"
