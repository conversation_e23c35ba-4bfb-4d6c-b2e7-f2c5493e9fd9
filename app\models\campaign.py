"""
Campaign model for storing Meta campaign information.
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class Campaign(Base):
    """Campaign model for storing Meta campaign data."""
    
    __tablename__ = "campaigns"
    
    id = Column(Integer, primary_key=True, index=True)
    meta_account_id = Column(Integer, ForeignKey("meta_accounts.id"), nullable=False)
    campaign_id = Column(String, nullable=False, index=True)  # Meta campaign ID
    name = Column(String, nullable=False)
    status = Column(String, nullable=False)  # ACTIVE, PAUSED, DELETED, etc.
    objective = Column(String, nullable=True)  # CONVERSIONS, TRAFFIC, etc.
    created_time = Column(DateTime(timezone=True), nullable=True)
    start_time = Column(DateTime(timezone=True), nullable=True)
    stop_time = Column(DateTime(timezone=True), nullable=True)
    budget_remaining = Column(String, nullable=True)
    daily_budget = Column(String, nullable=True)
    lifetime_budget = Column(String, nullable=True)
    bid_strategy = Column(String, nullable=True)
    additional_data = Column(JSON, nullable=True)  # Store additional Meta API data
    last_synced_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    meta_account = relationship("MetaAccount", back_populates="campaigns")
    adsets = relationship("AdSet", back_populates="campaign")
    performance_metrics = relationship("PerformanceMetric", back_populates="campaign")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, campaign_id='{self.campaign_id}', name='{self.name}')>"
