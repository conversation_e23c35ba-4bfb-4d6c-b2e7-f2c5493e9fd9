"""
Authentication routes for AD Engine chatbot.
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from sqlalchemy.orm import Session
from authlib.integrations.starlette_client import OAuth
from starlette.requests import Request
from starlette.responses import RedirectResponse
from app.database import get_db
from app.models.user import User
from app.models.meta_account import MetaAccount
from app.schemas.user import UserCreate, UserResponse
from app.schemas.auth import Token
from app.utils.auth import verify_password, get_password_hash, create_access_token
from app.utils.encryption import encrypt_token
from app.config import get_settings
from loguru import logger

settings = get_settings()
router = APIRouter()

# OAuth configuration
oauth = OAuth()
oauth.register(
    name='facebook',
    client_id=settings.meta_app_id,
    client_secret=settings.meta_app_secret,
    server_metadata_url='https://www.facebook.com/.well-known/oauth_authorization_server',
    client_kwargs={
        'scope': 'ads_management ads_read'
    }
)


@router.post("/register", response_model=UserResponse)
async def register_user(user: UserCreate, db: Session = Depends(get_db)):
    """Register a new user."""
    # Check if user already exists
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user.password) if user.password else None
    db_user = User(
        email=user.email,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login user with email and password."""
    user = db.query(User).filter(User.email == form_data.username).first()
    
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token = create_access_token(
        data={"sub": user.email, "user_id": user.id}
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/meta/oauth")
async def meta_oauth_init(request: Request):
    """Initiate Meta OAuth flow."""
    redirect_uri = settings.meta_redirect_uri
    return await oauth.facebook.authorize_redirect(request, redirect_uri)


@router.post("/meta/callback")
async def meta_oauth_callback(
    request: Request,
    db: Session = Depends(get_db)
):
    """Handle Meta OAuth callback."""
    try:
        token = await oauth.facebook.authorize_access_token(request)
        access_token = token.get('access_token')
        
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to get access token from Meta"
            )
        
        # For now, return the token - in production, you'd associate with user
        # This is a simplified implementation for Week 1
        return {
            "message": "Meta OAuth successful",
            "access_token": access_token[:20] + "..."  # Truncated for security
        }
        
    except Exception as e:
        logger.error(f"Meta OAuth callback error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="OAuth callback failed"
        )
