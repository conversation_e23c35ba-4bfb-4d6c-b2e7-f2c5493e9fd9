"""
Encryption utilities for secure token storage.
"""
from cryptography.fernet import <PERSON><PERSON><PERSON>
from app.config import get_settings
import base64

settings = get_settings()

# Initialize Fernet cipher
def get_cipher():
    """Get Fernet cipher instance."""
    # Ensure the key is properly formatted
    key = settings.encryption_key.encode() if isinstance(settings.encryption_key, str) else settings.encryption_key
    # If key is not 32 bytes, derive it properly
    if len(key) != 32:
        key = base64.urlsafe_b64encode(key[:32].ljust(32, b'0'))
    else:
        key = base64.urlsafe_b64encode(key)
    return <PERSON><PERSON><PERSON>(key)


def encrypt_token(token: str) -> str:
    """Encrypt a token for secure storage."""
    cipher = get_cipher()
    encrypted_token = cipher.encrypt(token.encode())
    return encrypted_token.decode()


def decrypt_token(encrypted_token: str) -> str:
    """Decrypt a token from storage."""
    cipher = get_cipher()
    decrypted_token = cipher.decrypt(encrypted_token.encode())
    return decrypted_token.decode()
