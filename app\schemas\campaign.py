"""
Campaign schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any


class CampaignBase(BaseModel):
    """Base campaign schema."""
    campaign_id: str
    name: str
    status: str
    objective: Optional[str] = None


class CampaignCreate(CampaignBase):
    """Schema for creating a campaign."""
    meta_account_id: int
    created_time: Optional[datetime] = None
    additional_data: Optional[Dict[str, Any]] = None


class CampaignResponse(CampaignBase):
    """Schema for campaign response."""
    id: int
    meta_account_id: int
    created_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    stop_time: Optional[datetime] = None
    budget_remaining: Optional[str] = None
    daily_budget: Optional[str] = None
    lifetime_budget: Optional[str] = None
    bid_strategy: Optional[str] = None
    last_synced_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
