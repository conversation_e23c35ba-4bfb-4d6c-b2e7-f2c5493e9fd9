"""
Meta Marketing API client for data retrieval.
"""
import httpx
from typing import Dict, List, Optional, Any
from app.config import get_settings
from loguru import logger

settings = get_settings()


class MetaAPIClient:
    """Client for interacting with Meta Marketing API."""
    
    BASE_URL = "https://graph.facebook.com/v21.0"
    
    def __init__(self, access_token: str):
        """Initialize Meta API client with access token."""
        self.access_token = access_token
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make authenticated request to Meta API."""
        if params is None:
            params = {}
        
        params["access_token"] = self.access_token
        url = f"{self.BASE_URL}/{endpoint}"
        
        try:
            response = await self.client.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Meta API error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Meta API request failed: {str(e)}")
            raise
    
    async def get_ad_accounts(self) -> List[Dict[str, Any]]:
        """Get list of ad accounts."""
        response = await self._make_request("me/adaccounts", {
            "fields": "id,name,account_status,currency,timezone_name"
        })
        return response.get("data", [])
    
    async def get_campaigns(self, account_id: str) -> List[Dict[str, Any]]:
        """Get campaigns for an ad account."""
        response = await self._make_request(f"{account_id}/campaigns", {
            "fields": "id,name,status,objective,created_time,start_time,stop_time,budget_remaining,daily_budget,lifetime_budget,bid_strategy"
        })
        return response.get("data", [])
    
    async def get_adsets(self, campaign_id: str) -> List[Dict[str, Any]]:
        """Get ad sets for a campaign."""
        response = await self._make_request(f"{campaign_id}/adsets", {
            "fields": "id,name,status,targeting,budget_remaining,daily_budget,lifetime_budget,bid_amount,optimization_goal,billing_event,created_time,start_time,end_time"
        })
        return response.get("data", [])
    
    async def get_ads(self, adset_id: str) -> List[Dict[str, Any]]:
        """Get ads for an ad set."""
        response = await self._make_request(f"{adset_id}/ads", {
            "fields": "id,name,status,creative,created_time"
        })
        return response.get("data", [])
    
    async def get_insights(self, entity_id: str, date_range: str = "last_7_days") -> List[Dict[str, Any]]:
        """Get performance insights for an entity."""
        response = await self._make_request(f"{entity_id}/insights", {
            "fields": "impressions,clicks,spend,conversions,conversion_values,reach,frequency,ctr,cpc,cpm",
            "date_preset": date_range,
            "level": "ad"
        })
        return response.get("data", [])
