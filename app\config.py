"""
Configuration management for AD Engine chatbot.
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Environment Configuration
    environment: str = "development"
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # Database Configuration
    database_url: str
    
    # OpenAI Configuration
    openai_api_key: str
    
    # Meta API Configuration
    meta_app_id: str
    meta_app_secret: str
    meta_redirect_uri: str = "http://localhost:8000/auth/meta/callback"
    
    # Security Configuration
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    encryption_key: str
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    
    # Logging Configuration
    log_level: str = "INFO"
    log_file: str = "logs/ad_engine.log"
    
    # API Configuration
    api_v1_prefix: str = "/api/v1"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
