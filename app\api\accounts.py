"""
Account management routes for AD Engine chatbot.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.models.user import User
from app.models.meta_account import MetaAccount
from app.schemas.meta_account import MetaAccountResponse
from app.api.deps import get_current_active_user
from app.utils.meta_api import MetaAPIClient
from app.utils.encryption import decrypt_token
from loguru import logger

router = APIRouter()


@router.get("/", response_model=List[MetaAccountResponse])
async def get_user_accounts(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all Meta accounts for the current user."""
    accounts = db.query(MetaAccount).filter(
        MetaAccount.user_id == current_user.id,
        MetaAccount.is_active == True
    ).all()
    
    return accounts


@router.get("/{account_id}/campaigns")
async def get_account_campaigns(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get campaigns for a specific Meta account."""
    # Get the Meta account
    meta_account = db.query(MetaAccount).filter(
        MetaAccount.id == account_id,
        MetaAccount.user_id == current_user.id,
        MetaAccount.is_active == True
    ).first()
    
    if not meta_account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta account not found"
        )
    
    try:
        # Decrypt access token and fetch campaigns
        access_token = decrypt_token(meta_account.access_token_encrypted)
        
        async with MetaAPIClient(access_token) as client:
            campaigns = await client.get_campaigns(meta_account.account_id)
            
        return {
            "account_id": meta_account.account_id,
            "campaigns": campaigns
        }
        
    except Exception as e:
        logger.error(f"Error fetching campaigns: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch campaigns from Meta API"
        )


@router.get("/{account_id}/performance")
async def get_account_performance(
    account_id: int,
    date_range: str = "last_7_days",
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get performance data for a specific Meta account."""
    # Get the Meta account
    meta_account = db.query(MetaAccount).filter(
        MetaAccount.id == account_id,
        MetaAccount.user_id == current_user.id,
        MetaAccount.is_active == True
    ).first()
    
    if not meta_account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Meta account not found"
        )
    
    try:
        # Decrypt access token and fetch performance data
        access_token = decrypt_token(meta_account.access_token_encrypted)
        
        async with MetaAPIClient(access_token) as client:
            insights = await client.get_insights(meta_account.account_id, date_range)
            
        return {
            "account_id": meta_account.account_id,
            "date_range": date_range,
            "performance": insights
        }
        
    except Exception as e:
        logger.error(f"Error fetching performance data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch performance data from Meta API"
        )
