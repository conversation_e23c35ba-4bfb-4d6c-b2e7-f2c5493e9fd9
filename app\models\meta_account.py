"""
Meta Account model for storing Meta ad account information.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class MetaAccount(Base):
    """Meta Account model for storing Meta ad account credentials."""
    
    __tablename__ = "meta_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    account_id = Column(String, nullable=False, index=True)  # Meta ad account ID
    account_name = Column(String, nullable=True)
    access_token_encrypted = Column(Text, nullable=False)  # Encrypted access token
    refresh_token_encrypted = Column(Text, nullable=True)  # Encrypted refresh token
    token_expires_at = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(String, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="meta_accounts")
    campaigns = relationship("Campaign", back_populates="meta_account")
    
    def __repr__(self):
        return f"<MetaAccount(id={self.id}, account_id='{self.account_id}')>"
