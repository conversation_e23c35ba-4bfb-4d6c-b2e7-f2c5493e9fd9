"""
Performance Metric model for storing ad performance data.
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Date
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base


class PerformanceMetric(Base):
    """Performance Metric model for storing ad performance data."""
    
    __tablename__ = "performance_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    entity_id = Column(String, nullable=False, index=True)  # Can be campaign, adset, or ad ID
    entity_type = Column(String, nullable=False)  # 'campaign', 'adset', 'ad'
    date = Column(Date, nullable=False, index=True)
    
    # Foreign keys for relationships
    campaign_id = Column(Integer, ForeignKey("campaigns.id"), nullable=True)
    adset_id = Column(Integer, ForeignKey("adsets.id"), nullable=True)
    ad_id = Column(Integer, ForeignKey("ads.id"), nullable=True)
    
    # Performance metrics
    impressions = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    spend = Column(Float, default=0.0)
    conversions = Column(Integer, default=0)
    conversion_value = Column(Float, default=0.0)
    reach = Column(Integer, default=0)
    frequency = Column(Float, default=0.0)
    
    # Calculated metrics
    ctr = Column(Float, default=0.0)  # Click-through rate
    cpc = Column(Float, default=0.0)  # Cost per click
    cpm = Column(Float, default=0.0)  # Cost per mille
    roas = Column(Float, default=0.0)  # Return on ad spend
    cost_per_conversion = Column(Float, default=0.0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    campaign = relationship("Campaign", back_populates="performance_metrics")
    adset = relationship("AdSet", back_populates="performance_metrics")
    ad = relationship("Ad", back_populates="performance_metrics")
    
    def __repr__(self):
        return f"<PerformanceMetric(id={self.id}, entity_id='{self.entity_id}', entity_type='{self.entity_type}', date='{self.date}')>"
