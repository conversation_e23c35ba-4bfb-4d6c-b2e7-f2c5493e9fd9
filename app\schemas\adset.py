"""
AdSet schemas for request/response validation.
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any


class AdSetResponse(BaseModel):
    """Schema for adset response."""
    id: int
    campaign_id: int
    adset_id: str
    name: str
    status: str
    targeting: Optional[Dict[str, Any]] = None
    budget_remaining: Optional[str] = None
    daily_budget: Optional[str] = None
    lifetime_budget: Optional[str] = None
    bid_amount: Optional[str] = None
    optimization_goal: Optional[str] = None
    billing_event: Optional[str] = None
    created_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    last_synced_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
