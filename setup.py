"""
Setup script for AD Engine chatbot development environment.
"""
import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False


def setup_environment():
    """Setup the development environment."""
    print("🚀 Setting up AD Engine Chatbot Development Environment")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    print("✅ Logs directory created")
    
    # Check if .env exists
    if not Path(".env").exists():
        print("\n⚠️  .env file not found. Please:")
        print("1. Copy .env.example to .env")
        print("2. Fill in your configuration values")
        print("3. Run this setup script again")
        return False
    
    print("✅ .env file found")
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    
    # Initialize Alembic (if not already done)
    if not Path("alembic/versions").exists():
        if not run_command("alembic revision --autogenerate -m 'Initial migration'", "Creating initial database migration"):
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Ensure PostgreSQL is running")
    print("2. Create database: createdb ad_engine_db")
    print("3. Run migrations: alembic upgrade head")
    print("4. Start the server: python run.py")
    print("5. Visit http://localhost:8000/docs for API documentation")
    
    return True


if __name__ == "__main__":
    success = setup_environment()
    sys.exit(0 if success else 1)
