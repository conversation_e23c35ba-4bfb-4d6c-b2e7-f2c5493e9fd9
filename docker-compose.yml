version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/ad_engine_db
      - ENVIRONMENT=development
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    env_file:
      - .env

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=ad_engine_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
